import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  Modal,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import {
  SSHConfig,
  ConnectionStatus,
  FileSystemItem,
  CodeSelection,
  AIAssistantType,
  AIAssistantConfig,
  AIAssistantStatus,
} from '../types';
import { SSHService } from '../services/SSHService';
import { AgentFactory } from '../services/agents/AgentFactory';
import { AIAgent } from '../types';
import { FileSystemBrowser } from '../components/FileSystemBrowser';
import { CodeViewer } from '../components/CodeViewer';
import { AIAssistantChat } from '../components/AIAssistantChat';
import { SSHConnectionModal } from '../components/SSHConnectionModal';

export const MainScreen: React.FC = () => {
  // SSH连接状态
  const [sshService] = useState(() => new SSHService());
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>(
    ConnectionStatus.DISCONNECTED
  );
  const [showConnectionModal, setShowConnectionModal] = useState(false);

  // 文件系统状态
  const [currentPath, setCurrentPath] = useState('/home');
  const [selectedFile, setSelectedFile] = useState<FileSystemItem | null>(null);
  const [showCodeViewer, setShowCodeViewer] = useState(false);

  // AI助手状态
  const [aiAgent, setAiAgent] = useState<AIAgent | null>(null);
  const [aiStatus, setAiStatus] = useState<AIAssistantStatus>(AIAssistantStatus.IDLE);
  const [showAIChat, setShowAIChat] = useState(false);

  // 代码选择状态
  const [codeSelection, setCodeSelection] = useState<CodeSelection | null>(null);
  const [selectedFilePath, setSelectedFilePath] = useState<string | null>(null);

  // 界面状态
  const [activeTab, setActiveTab] = useState<'files' | 'chat'>('files');

  useEffect(() => {
    // 监听SSH连接状态变化
    sshService.onStatusChange((status) => {
      setConnectionStatus(status);
      if (status === ConnectionStatus.DISCONNECTED && aiAgent) {
        // SSH断开时停止AI助手
        aiAgent.stop();
        setAiAgent(null);
        setAiStatus(AIAssistantStatus.IDLE);
      }
    });
  }, [sshService, aiAgent]);

  const handleConnect = async (config: SSHConfig) => {
    try {
      await sshService.connect(config);
      setShowConnectionModal(false);
      Alert.alert('成功', 'SSH连接已建立');
    } catch (error) {
      Alert.alert('连接失败', `无法连接到服务器: ${error}`);
    }
  };

  const handleDisconnect = async () => {
    try {
      if (aiAgent) {
        await aiAgent.stop();
        setAiAgent(null);
        setAiStatus(AIAssistantStatus.IDLE);
      }
      await sshService.disconnect();
      Alert.alert('已断开', 'SSH连接已断开');
    } catch (error) {
      Alert.alert('错误', `断开连接时出错: ${error}`);
    }
  };

  const handleFileSelect = (file: FileSystemItem) => {
    setSelectedFile(file);
    setShowCodeViewer(true);
  };

  const handleCodeSelect = (selection: CodeSelection) => {
    setCodeSelection(selection);
    setSelectedFilePath(selectedFile?.path || null);
    setShowCodeViewer(false);
    setActiveTab('chat');
  };

  const handleClearCodeContext = () => {
    setCodeSelection(null);
    setSelectedFilePath(null);
  };

  const startAIAssistant = async () => {
    if (connectionStatus !== ConnectionStatus.CONNECTED) {
      Alert.alert('错误', '请先建立SSH连接');
      return;
    }

    try {
      const agent = AgentFactory.createAgent(AIAssistantType.CLAUDE_CODE, sshService);
      
      agent.onStatusChange((status) => {
        setAiStatus(status);
      });

      const config: AIAssistantConfig = {
        type: AIAssistantType.CLAUDE_CODE,
        projectPath: currentPath,
      };

      await agent.start(config);
      setAiAgent(agent);
      setShowAIChat(true);
      setActiveTab('chat');
      
      Alert.alert('成功', 'Claude Code已启动');
    } catch (error) {
      Alert.alert('启动失败', `无法启动AI助手: ${error}`);
    }
  };

  const stopAIAssistant = async () => {
    if (aiAgent) {
      try {
        await aiAgent.stop();
        setAiAgent(null);
        setAiStatus(AIAssistantStatus.IDLE);
        setShowAIChat(false);
        Alert.alert('已停止', 'AI助手已停止');
      } catch (error) {
        Alert.alert('错误', `停止AI助手时出错: ${error}`);
      }
    }
  };

  const renderConnectionStatus = () => {
    const getStatusColor = () => {
      switch (connectionStatus) {
        case ConnectionStatus.CONNECTED:
          return '#4CAF50';
        case ConnectionStatus.CONNECTING:
          return '#FF9800';
        case ConnectionStatus.ERROR:
          return '#F44336';
        default:
          return '#9E9E9E';
      }
    };

    const getStatusText = () => {
      switch (connectionStatus) {
        case ConnectionStatus.CONNECTED:
          return '已连接';
        case ConnectionStatus.CONNECTING:
          return '连接中...';
        case ConnectionStatus.ERROR:
          return '连接错误';
        default:
          return '未连接';
      }
    };

    return (
      <View style={styles.statusContainer}>
        <View style={styles.statusIndicator}>
          <View style={[styles.statusDot, { backgroundColor: getStatusColor() }]} />
          <Text style={styles.statusText}>SSH: {getStatusText()}</Text>
        </View>
        
        <View style={styles.statusActions}>
          {connectionStatus === ConnectionStatus.CONNECTED ? (
            <TouchableOpacity style={styles.disconnectButton} onPress={handleDisconnect}>
              <Text style={styles.buttonText}>断开</Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={styles.connectButton}
              onPress={() => setShowConnectionModal(true)}
            >
              <Text style={styles.buttonText}>连接</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  const renderAIStatus = () => {
    if (connectionStatus !== ConnectionStatus.CONNECTED) {
      return null;
    }

    const getAIStatusColor = () => {
      switch (aiStatus) {
        case AIAssistantStatus.READY:
          return '#4CAF50';
        case AIAssistantStatus.PROCESSING:
          return '#FF9800';
        case AIAssistantStatus.ERROR:
          return '#F44336';
        default:
          return '#9E9E9E';
      }
    };

    const getAIStatusText = () => {
      switch (aiStatus) {
        case AIAssistantStatus.READY:
          return '就绪';
        case AIAssistantStatus.STARTING:
          return '启动中...';
        case AIAssistantStatus.PROCESSING:
          return '处理中...';
        case AIAssistantStatus.ERROR:
          return '错误';
        default:
          return '未启动';
      }
    };

    return (
      <View style={styles.statusContainer}>
        <View style={styles.statusIndicator}>
          <View style={[styles.statusDot, { backgroundColor: getAIStatusColor() }]} />
          <Text style={styles.statusText}>AI助手: {getAIStatusText()}</Text>
        </View>
        
        <View style={styles.statusActions}>
          {aiAgent ? (
            <TouchableOpacity style={styles.disconnectButton} onPress={stopAIAssistant}>
              <Text style={styles.buttonText}>停止</Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity style={styles.connectButton} onPress={startAIAssistant}>
              <Text style={styles.buttonText}>启动</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  const renderTabBar = () => (
    <View style={styles.tabBar}>
      <TouchableOpacity
        style={[styles.tab, activeTab === 'files' && styles.activeTab]}
        onPress={() => setActiveTab('files')}
      >
        <Text style={[styles.tabText, activeTab === 'files' && styles.activeTabText]}>
          📁 文件
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[styles.tab, activeTab === 'chat' && styles.activeTab]}
        onPress={() => setActiveTab('chat')}
        disabled={!aiAgent}
      >
        <Text
          style={[
            styles.tabText,
            activeTab === 'chat' && styles.activeTabText,
            !aiAgent && styles.disabledTabText,
          ]}
        >
          🤖 AI助手
        </Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#fff" />
      
      {/* 头部状态栏 */}
      <View style={styles.header}>
        <Text style={styles.title}>远程编码助手</Text>
        {renderConnectionStatus()}
        {renderAIStatus()}
      </View>

      {/* 标签栏 */}
      {renderTabBar()}

      {/* 主内容区域 */}
      <View style={styles.content}>
        {activeTab === 'files' && connectionStatus === ConnectionStatus.CONNECTED && (
          <FileSystemBrowser
            sshService={sshService}
            onFileSelect={handleFileSelect}
            onDirectoryChange={setCurrentPath}
            currentPath={currentPath}
          />
        )}

        {activeTab === 'chat' && aiAgent && (
          <AIAssistantChat
            agent={aiAgent}
            codeContext={codeSelection}
            filePath={selectedFilePath}
            onClearContext={handleClearCodeContext}
          />
        )}

        {connectionStatus !== ConnectionStatus.CONNECTED && (
          <View style={styles.emptyState}>
            <Text style={styles.emptyStateText}>请先建立SSH连接</Text>
          </View>
        )}
      </View>

      {/* SSH连接模态框 */}
      <SSHConnectionModal
        visible={showConnectionModal}
        onConnect={handleConnect}
        onCancel={() => setShowConnectionModal(false)}
      />

      {/* 代码查看器模态框 */}
      <Modal
        visible={showCodeViewer}
        animationType="slide"
        presentationStyle="fullScreen"
      >
        {selectedFile && (
          <CodeViewer
            sshService={sshService}
            file={selectedFile}
            onCodeSelect={handleCodeSelect}
            onClose={() => setShowCodeViewer(false)}
          />
        )}
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#fff',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  statusText: {
    fontSize: 14,
    color: '#666',
  },
  statusActions: {
    flexDirection: 'row',
  },
  connectButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  disconnectButton: {
    backgroundColor: '#F44336',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  buttonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#007AFF',
  },
  tabText: {
    fontSize: 16,
    color: '#666',
  },
  activeTabText: {
    color: '#007AFF',
    fontWeight: '500',
  },
  disabledTabText: {
    color: '#ccc',
  },
  content: {
    flex: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666',
  },
});
