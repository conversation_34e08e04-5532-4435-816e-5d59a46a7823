import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { FileSystemItem } from '../types';
import { SSHService } from '../services/SSHService';
import { getFileIcon, formatFileSize } from '../utils/fileUtils';

interface FileSystemBrowserProps {
  sshService: SSHService;
  onFileSelect: (file: FileSystemItem) => void;
  onDirectoryChange: (path: string) => void;
  currentPath: string;
}

export const FileSystemBrowser: React.FC<FileSystemBrowserProps> = ({
  sshService,
  onFileSelect,
  onDirectoryChange,
  currentPath,
}) => {
  const [items, setItems] = useState<FileSystemItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadDirectory(currentPath);
  }, [currentPath]);

  const loadDirectory = async (path: string) => {
    if (!sshService.isConnected()) {
      setError('SSH连接未建立');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const directoryItems = await sshService.listDirectory(path);
      setItems(directoryItems);
    } catch (err) {
      setError(`加载目录失败: ${err}`);
      Alert.alert('错误', `无法加载目录: ${err}`);
    } finally {
      setLoading(false);
    }
  };

  const handleItemPress = (item: FileSystemItem) => {
    if (item.type === 'directory') {
      onDirectoryChange(item.path);
    } else {
      onFileSelect(item);
    }
  };

  const navigateUp = () => {
    const parentPath = currentPath.split('/').slice(0, -1).join('/') || '/';
    onDirectoryChange(parentPath);
  };

  const getItemIcon = (item: FileSystemItem) => {
    return getFileIcon(item.name, item.type === 'directory');
  };

  const renderItem = ({ item }: { item: FileSystemItem }) => (
    <TouchableOpacity
      style={styles.itemContainer}
      onPress={() => handleItemPress(item)}
    >
      <View style={styles.itemContent}>
        <Text style={styles.icon}>{getItemIcon(item)}</Text>
        <View style={styles.itemInfo}>
          <Text style={styles.itemName}>{item.name}</Text>
          <View style={styles.itemDetails}>
            <Text style={styles.itemSize}>{formatFileSize(item.size)}</Text>
            <Text style={styles.itemPermissions}>{item.permissions}</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>加载中...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* 路径导航 */}
      <View style={styles.pathContainer}>
        <TouchableOpacity style={styles.upButton} onPress={navigateUp}>
          <Text style={styles.upButtonText}>⬆️ 上级目录</Text>
        </TouchableOpacity>
        <Text style={styles.currentPath}>{currentPath}</Text>
      </View>

      {/* 错误提示 */}
      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}

      {/* 文件列表 */}
      <FlatList
        data={items}
        renderItem={renderItem}
        keyExtractor={(item) => item.path}
        style={styles.fileList}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  pathContainer: {
    backgroundColor: '#fff',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  upButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    alignSelf: 'flex-start',
    marginBottom: 8,
  },
  upButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  currentPath: {
    fontSize: 14,
    color: '#666',
    fontFamily: 'monospace',
  },
  errorContainer: {
    backgroundColor: '#ffebee',
    padding: 12,
    margin: 12,
    borderRadius: 6,
    borderLeftWidth: 4,
    borderLeftColor: '#f44336',
  },
  errorText: {
    color: '#c62828',
    fontSize: 14,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  fileList: {
    flex: 1,
  },
  itemContainer: {
    backgroundColor: '#fff',
    marginHorizontal: 12,
    marginVertical: 2,
    borderRadius: 6,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  itemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  icon: {
    fontSize: 24,
    marginRight: 12,
  },
  itemInfo: {
    flex: 1,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  itemDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  itemSize: {
    fontSize: 12,
    color: '#666',
  },
  itemPermissions: {
    fontSize: 12,
    color: '#666',
    fontFamily: 'monospace',
  },
});
