import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { AIMessage, AIAssistantStatus, CodeSelection } from '../types';
import { AIAgent } from '../types';

interface AIAssistantChatProps {
  agent: AIAgent;
  codeContext?: CodeSelection;
  filePath?: string;
  onClearContext: () => void;
}

export const AIAssistantChat: React.FC<AIAssistantChatProps> = ({
  agent,
  codeContext,
  filePath,
  onClearContext,
}) => {
  const [messages, setMessages] = useState<AIMessage[]>([]);
  const [inputText, setInputText] = useState('');
  const [status, setStatus] = useState<AIAssistantStatus>(AIAssistantStatus.IDLE);
  const [isTyping, setIsTyping] = useState(false);
  const flatListRef = useRef<FlatList>(null);

  useEffect(() => {
    // 监听Agent状态变化
    agent.onStatusChange((newStatus) => {
      setStatus(newStatus);
      if (newStatus === AIAssistantStatus.PROCESSING) {
        setIsTyping(true);
      } else {
        setIsTyping(false);
      }
    });

    // 监听Agent消息
    agent.onMessage((message) => {
      setMessages(prev => [...prev, message]);
      // 自动滚动到底部
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    });

    // 获取初始状态
    setStatus(agent.getStatus());
  }, [agent]);

  const sendMessage = async () => {
    if (!inputText.trim() || status !== AIAssistantStatus.READY) {
      return;
    }

    const messageText = inputText.trim();
    setInputText('');

    try {
      await agent.sendMessage(messageText, codeContext, filePath);
    } catch (error) {
      Alert.alert('错误', `发送消息失败: ${error}`);
    }
  };

  const getStatusText = () => {
    switch (status) {
      case AIAssistantStatus.IDLE:
        return '未启动';
      case AIAssistantStatus.STARTING:
        return '启动中...';
      case AIAssistantStatus.READY:
        return '就绪';
      case AIAssistantStatus.PROCESSING:
        return '处理中...';
      case AIAssistantStatus.ERROR:
        return '错误';
      default:
        return '未知状态';
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case AIAssistantStatus.READY:
        return '#4CAF50';
      case AIAssistantStatus.PROCESSING:
        return '#FF9800';
      case AIAssistantStatus.ERROR:
        return '#F44336';
      default:
        return '#9E9E9E';
    }
  };

  const renderMessage = ({ item }: { item: AIMessage }) => (
    <View
      style={[
        styles.messageContainer,
        item.type === 'user' ? styles.userMessage : styles.assistantMessage,
      ]}
    >
      <View
        style={[
          styles.messageBubble,
          item.type === 'user' ? styles.userBubble : styles.assistantBubble,
        ]}
      >
        {/* 代码上下文显示 */}
        {item.codeContext && item.filePath && (
          <View style={styles.codeContext}>
            <Text style={styles.codeContextHeader}>
              📄 {item.filePath} (第{item.codeContext.startLine}-{item.codeContext.endLine}行)
            </Text>
            <View style={styles.codeContextContent}>
              <Text style={styles.codeContextText}>
                {item.codeContext.selectedText}
              </Text>
            </View>
          </View>
        )}
        
        <Text
          style={[
            styles.messageText,
            item.type === 'user' ? styles.userText : styles.assistantText,
          ]}
        >
          {item.content}
        </Text>
        
        <Text
          style={[
            styles.messageTime,
            item.type === 'user' ? styles.userTime : styles.assistantTime,
          ]}
        >
          {item.timestamp.toLocaleTimeString()}
        </Text>
      </View>
    </View>
  );

  const renderTypingIndicator = () => {
    if (!isTyping) return null;

    return (
      <View style={[styles.messageContainer, styles.assistantMessage]}>
        <View style={[styles.messageBubble, styles.assistantBubble]}>
          <View style={styles.typingIndicator}>
            <ActivityIndicator size="small" color="#666" />
            <Text style={styles.typingText}>AI助手正在思考...</Text>
          </View>
        </View>
      </View>
    );
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {/* 状态栏 */}
      <View style={styles.statusBar}>
        <View style={styles.statusIndicator}>
          <View
            style={[styles.statusDot, { backgroundColor: getStatusColor() }]}
          />
          <Text style={styles.statusText}>{getStatusText()}</Text>
        </View>
        
        {/* 代码上下文显示 */}
        {codeContext && filePath && (
          <View style={styles.contextInfo}>
            <Text style={styles.contextText}>
              📄 {filePath.split('/').pop()} (第{codeContext.startLine}-{codeContext.endLine}行)
            </Text>
            <TouchableOpacity style={styles.clearContextButton} onPress={onClearContext}>
              <Text style={styles.clearContextText}>✕</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* 消息列表 */}
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        style={styles.messagesList}
        showsVerticalScrollIndicator={false}
        ListFooterComponent={renderTypingIndicator}
      />

      {/* 输入区域 */}
      <View style={styles.inputContainer}>
        <TextInput
          style={styles.textInput}
          value={inputText}
          onChangeText={setInputText}
          placeholder="输入消息..."
          placeholderTextColor="#999"
          multiline
          maxLength={1000}
          editable={status === AIAssistantStatus.READY}
        />
        <TouchableOpacity
          style={[
            styles.sendButton,
            (status !== AIAssistantStatus.READY || !inputText.trim()) &&
              styles.sendButtonDisabled,
          ]}
          onPress={sendMessage}
          disabled={status !== AIAssistantStatus.READY || !inputText.trim()}
        >
          <Text style={styles.sendButtonText}>发送</Text>
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  statusBar: {
    backgroundColor: '#fff',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  statusText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  contextInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#e3f2fd',
    padding: 8,
    borderRadius: 6,
    borderLeftWidth: 3,
    borderLeftColor: '#2196f3',
  },
  contextText: {
    flex: 1,
    fontSize: 12,
    color: '#1976d2',
  },
  clearContextButton: {
    padding: 4,
    marginLeft: 8,
  },
  clearContextText: {
    color: '#1976d2',
    fontSize: 16,
    fontWeight: 'bold',
  },
  messagesList: {
    flex: 1,
    paddingHorizontal: 12,
  },
  messageContainer: {
    marginVertical: 4,
  },
  userMessage: {
    alignItems: 'flex-end',
  },
  assistantMessage: {
    alignItems: 'flex-start',
  },
  messageBubble: {
    maxWidth: '80%',
    padding: 12,
    borderRadius: 16,
  },
  userBubble: {
    backgroundColor: '#007AFF',
  },
  assistantBubble: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  codeContext: {
    marginBottom: 8,
    padding: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    borderRadius: 6,
  },
  codeContextHeader: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
    fontWeight: '500',
  },
  codeContextContent: {
    backgroundColor: '#f8f8f8',
    padding: 8,
    borderRadius: 4,
    borderLeftWidth: 3,
    borderLeftColor: '#007AFF',
  },
  codeContextText: {
    fontSize: 12,
    fontFamily: 'monospace',
    color: '#333',
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
  },
  userText: {
    color: '#fff',
  },
  assistantText: {
    color: '#333',
  },
  messageTime: {
    fontSize: 12,
    marginTop: 4,
  },
  userTime: {
    color: 'rgba(255, 255, 255, 0.7)',
  },
  assistantTime: {
    color: '#999',
  },
  typingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typingText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: '#fff',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    maxHeight: 100,
    fontSize: 16,
    color: '#333',
  },
  sendButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    marginLeft: 8,
  },
  sendButtonDisabled: {
    backgroundColor: '#ccc',
  },
  sendButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
});
