import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Modal,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { SSHConfig } from '../types';

interface SSHConnectionModalProps {
  visible: boolean;
  onConnect: (config: SSHConfig) => void;
  onCancel: () => void;
}

export const SSHConnectionModal: React.FC<SSHConnectionModalProps> = ({
  visible,
  onConnect,
  onCancel,
}) => {
  const [host, setHost] = useState('');
  const [port, setPort] = useState('22');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [usePrivateKey, setUsePrivateKey] = useState(false);
  const [privateKey, setPrivateKey] = useState('');
  const [passphrase, setPassphrase] = useState('');

  const handleConnect = () => {
    if (!host.trim() || !username.trim()) {
      Alert.alert('错误', '请填写主机地址和用户名');
      return;
    }

    if (!usePrivateKey && !password.trim()) {
      Alert.alert('错误', '请填写密码或选择使用私钥');
      return;
    }

    if (usePrivateKey && !privateKey.trim()) {
      Alert.alert('错误', '请填写私钥内容');
      return;
    }

    const config: SSHConfig = {
      host: host.trim(),
      port: parseInt(port) || 22,
      username: username.trim(),
      password: usePrivateKey ? undefined : password,
      privateKey: usePrivateKey ? privateKey.trim() : undefined,
      passphrase: passphrase.trim() || undefined,
    };

    onConnect(config);
  };

  const resetForm = () => {
    setHost('');
    setPort('22');
    setUsername('');
    setPassword('');
    setUsePrivateKey(false);
    setPrivateKey('');
    setPassphrase('');
  };

  const handleCancel = () => {
    resetForm();
    onCancel();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleCancel}
    >
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.header}>
          <TouchableOpacity style={styles.cancelButton} onPress={handleCancel}>
            <Text style={styles.cancelButtonText}>取消</Text>
          </TouchableOpacity>
          <Text style={styles.title}>SSH连接</Text>
          <TouchableOpacity style={styles.connectButton} onPress={handleConnect}>
            <Text style={styles.connectButtonText}>连接</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.form} showsVerticalScrollIndicator={false}>
          {/* 主机地址 */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>主机地址 *</Text>
            <TextInput
              style={styles.input}
              value={host}
              onChangeText={setHost}
              placeholder="例如: *************"
              placeholderTextColor="#999"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          {/* 端口 */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>端口</Text>
            <TextInput
              style={styles.input}
              value={port}
              onChangeText={setPort}
              placeholder="22"
              placeholderTextColor="#999"
              keyboardType="numeric"
            />
          </View>

          {/* 用户名 */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>用户名 *</Text>
            <TextInput
              style={styles.input}
              value={username}
              onChangeText={setUsername}
              placeholder="例如: root"
              placeholderTextColor="#999"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          {/* 认证方式选择 */}
          <View style={styles.authTypeContainer}>
            <TouchableOpacity
              style={[styles.authTypeButton, !usePrivateKey && styles.authTypeButtonActive]}
              onPress={() => setUsePrivateKey(false)}
            >
              <Text style={[styles.authTypeText, !usePrivateKey && styles.authTypeTextActive]}>
                密码认证
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.authTypeButton, usePrivateKey && styles.authTypeButtonActive]}
              onPress={() => setUsePrivateKey(true)}
            >
              <Text style={[styles.authTypeText, usePrivateKey && styles.authTypeTextActive]}>
                私钥认证
              </Text>
            </TouchableOpacity>
          </View>

          {/* 密码认证 */}
          {!usePrivateKey && (
            <View style={styles.inputGroup}>
              <Text style={styles.label}>密码 *</Text>
              <TextInput
                style={styles.input}
                value={password}
                onChangeText={setPassword}
                placeholder="请输入密码"
                placeholderTextColor="#999"
                secureTextEntry
              />
            </View>
          )}

          {/* 私钥认证 */}
          {usePrivateKey && (
            <>
              <View style={styles.inputGroup}>
                <Text style={styles.label}>私钥内容 *</Text>
                <TextInput
                  style={[styles.input, styles.multilineInput]}
                  value={privateKey}
                  onChangeText={setPrivateKey}
                  placeholder="请粘贴私钥内容（PEM格式）"
                  placeholderTextColor="#999"
                  multiline
                  numberOfLines={6}
                  textAlignVertical="top"
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.label}>私钥密码（可选）</Text>
                <TextInput
                  style={styles.input}
                  value={passphrase}
                  onChangeText={setPassphrase}
                  placeholder="如果私钥有密码请输入"
                  placeholderTextColor="#999"
                  secureTextEntry
                />
              </View>
            </>
          )}

          {/* 连接提示 */}
          <View style={styles.hintContainer}>
            <Text style={styles.hintText}>
              💡 提示：确保目标服务器已安装Claude Code，并且您有足够的权限访问项目目录。
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  cancelButton: {
    padding: 8,
  },
  cancelButtonText: {
    color: '#007AFF',
    fontSize: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  connectButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  connectButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  form: {
    flex: 1,
    padding: 16,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#fff',
    color: '#333',
  },
  multilineInput: {
    height: 120,
    textAlignVertical: 'top',
  },
  authTypeContainer: {
    flexDirection: 'row',
    marginBottom: 20,
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 4,
  },
  authTypeButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 6,
  },
  authTypeButtonActive: {
    backgroundColor: '#007AFF',
  },
  authTypeText: {
    fontSize: 16,
    color: '#666',
  },
  authTypeTextActive: {
    color: '#fff',
    fontWeight: '500',
  },
  hintContainer: {
    backgroundColor: '#e3f2fd',
    padding: 12,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#2196f3',
    marginTop: 20,
  },
  hintText: {
    fontSize: 14,
    color: '#1976d2',
    lineHeight: 20,
  },
});
