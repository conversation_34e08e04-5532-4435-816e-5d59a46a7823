import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { CodeSelection, FileSystemItem } from '../types';
import { SSHService } from '../services/SSHService';

interface CodeViewerProps {
  sshService: SSHService;
  file: FileSystemItem;
  onCodeSelect: (selection: CodeSelection) => void;
  onClose: () => void;
}

export const CodeViewer: React.FC<CodeViewerProps> = ({
  sshService,
  file,
  onCodeSelect,
  onClose,
}) => {
  const [content, setContent] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selection, setSelection] = useState<CodeSelection | null>(null);
  const [isSelecting, setIsSelecting] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  useEffect(() => {
    loadFileContent();
  }, [file]);

  const loadFileContent = async () => {
    setLoading(true);
    setError(null);

    try {
      const fileContent = await sshService.readFile(file.path);
      setContent(fileContent);
    } catch (err) {
      setError(`加载文件失败: ${err}`);
      Alert.alert('错误', `无法加载文件: ${err}`);
    } finally {
      setLoading(false);
    }
  };



  const renderLineNumbers = () => {
    const lines = content.split('\n');
    return (
      <View style={styles.lineNumbers}>
        {lines.map((_, index) => (
          <Text key={index} style={styles.lineNumber}>
            {index + 1}
          </Text>
        ))}
      </View>
    );
  };

  const renderCodeContent = () => {
    const lines = content.split('\n');

    return (
      <View style={styles.codeContent}>
        {lines.map((line, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.codeLine,
              selection &&
                index >= selection.startLine - 1 &&
                index <= selection.endLine - 1 &&
                styles.selectedLine,
            ]}
            onPress={() => handleLinePress(index + 1)}
            onLongPress={() => startSelection(index + 1)}
          >
            <Text style={styles.codeText}>{line || ' '}</Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const handleLinePress = (lineNumber: number) => {
    if (isSelecting && selection) {
      // 扩展选择
      const newSelection = {
        ...selection,
        endLine: lineNumber,
        selectedText: getSelectedText(selection.startLine, lineNumber),
      };
      setSelection(newSelection);
    } else {
      // 开始新选择
      startSelection(lineNumber);
    }
  };

  const startSelection = (lineNumber: number) => {
    const newSelection: CodeSelection = {
      startLine: lineNumber,
      endLine: lineNumber,
      startColumn: 0,
      endColumn: 0,
      selectedText: getSelectedText(lineNumber, lineNumber),
    };
    setSelection(newSelection);
    setIsSelecting(true);
  };

  const getSelectedText = (startLine: number, endLine: number): string => {
    const lines = content.split('\n');
    const start = Math.min(startLine, endLine) - 1;
    const end = Math.max(startLine, endLine) - 1;
    return lines.slice(start, end + 1).join('\n');
  };

  const confirmSelection = () => {
    if (selection) {
      onCodeSelect(selection);
      Alert.alert('代码已选择', '选中的代码将作为上下文发送给AI助手');
    }
    setIsSelecting(false);
  };

  const cancelSelection = () => {
    setSelection(null);
    setIsSelecting(false);
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>加载文件中...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={loadFileContent}>
          <Text style={styles.retryButtonText}>重试</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* 头部工具栏 */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Text style={styles.closeButtonText}>✕</Text>
        </TouchableOpacity>
        <Text style={styles.fileName}>{file.name}</Text>
        <View style={styles.headerActions}>
          {isSelecting && (
            <>
              <TouchableOpacity style={styles.actionButton} onPress={confirmSelection}>
                <Text style={styles.actionButtonText}>确认</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.actionButton} onPress={cancelSelection}>
                <Text style={styles.actionButtonText}>取消</Text>
              </TouchableOpacity>
            </>
          )}
        </View>
      </View>

      {/* 选择提示 */}
      {isSelecting && (
        <View style={styles.selectionHint}>
          <Text style={styles.selectionHintText}>
            点击行号选择代码，长按开始选择，再次点击扩展选择范围
          </Text>
        </View>
      )}

      {/* 代码内容 */}
      <ScrollView
        ref={scrollViewRef}
        style={styles.codeContainer}
        horizontal={true}
        showsHorizontalScrollIndicator={true}
        showsVerticalScrollIndicator={true}
      >
        <View style={styles.codeWrapper}>
          {renderLineNumbers()}
          {renderCodeContent()}
        </View>
      </ScrollView>

      {/* 选择信息 */}
      {selection && (
        <View style={styles.selectionInfo}>
          <Text style={styles.selectionInfoText}>
            已选择: 第{selection.startLine}行 - 第{selection.endLine}行
            ({selection.endLine - selection.startLine + 1}行)
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1e1e1e',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2d2d2d',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#404040',
  },
  closeButton: {
    padding: 8,
    marginRight: 12,
  },
  closeButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  fileName: {
    flex: 1,
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  headerActions: {
    flexDirection: 'row',
  },
  actionButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    marginLeft: 8,
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  selectionHint: {
    backgroundColor: '#333',
    padding: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#404040',
  },
  selectionHintText: {
    color: '#ccc',
    fontSize: 12,
    textAlign: 'center',
  },
  codeContainer: {
    flex: 1,
  },
  codeWrapper: {
    flexDirection: 'row',
    minWidth: Dimensions.get('window').width,
  },
  lineNumbers: {
    backgroundColor: '#2d2d2d',
    paddingHorizontal: 8,
    paddingVertical: 8,
    borderRightWidth: 1,
    borderRightColor: '#404040',
  },
  lineNumber: {
    color: '#666',
    fontSize: 12,
    fontFamily: 'monospace',
    lineHeight: 20,
    textAlign: 'right',
    minWidth: 40,
  },
  codeContent: {
    flex: 1,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  codeLine: {
    minHeight: 20,
    paddingVertical: 2,
  },
  selectedLine: {
    backgroundColor: '#264f78',
  },
  codeText: {
    color: '#d4d4d4',
    fontSize: 12,
    fontFamily: 'monospace',
    lineHeight: 20,
  },
  selectionInfo: {
    backgroundColor: '#2d2d2d',
    padding: 8,
    borderTopWidth: 1,
    borderTopColor: '#404040',
  },
  selectionInfoText: {
    color: '#ccc',
    fontSize: 12,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1e1e1e',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#ccc',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1e1e1e',
    padding: 20,
  },
  errorText: {
    color: '#f44336',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 6,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
});
